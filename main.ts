import { Readability } from "jsr:@paoramen/cheer-reader";

import ollama from "npm:ollama";
import * as cheerio from "npm:cheerio@1.0.0";

const searchUrl = Deno.env.get("SEARCH_URL");
const query = Deno.args.join(" ");

console.log(`Query: ${query}`);
console.log(`Search URL: ${searchUrl}`);
const urls = await getNewsUrls(query);
const alltexts = await getCleanedText(urls);
await answerQuery(query, alltexts);

async function getNewsUrls(query: string) {
  console.log('Using DuckDuckGo search as fallback due to SearXNG rate limits...');

  try {
    // Use DuckDuckGo's HTML search and parse results
    const encodedQuery = encodeURIComponent(query);
    const searchUrl = `https://html.duckduckgo.com/html/?q=${encodedQuery}`;

    const response = await fetch(searchUrl, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
    });

    if (!response.ok) {
      throw new Error(`DuckDuckGo search failed: ${response.status} ${response.statusText}`);
    }

    const html = await response.text();

    // Parse HTML to extract URLs using a simple regex approach
    // Look for result links in DuckDuckGo's HTML format
    const linkRegex = /class="result__url"[^>]*href="([^"]+)"/g;
    const urls: string[] = [];
    let match;

    while ((match = linkRegex.exec(html)) !== null && urls.length < 3) {
      const url = match[1];
      // Decode URL if it's encoded
      const decodedUrl = decodeURIComponent(url);

      // Filter out DuckDuckGo's own URLs and ensure it's a proper URL
      if (decodedUrl.startsWith('http') && !decodedUrl.includes('duckduckgo.com')) {
        urls.push(decodedUrl);
      }
    }

    if (urls.length === 0) {
      // Fallback: try a different regex pattern for DuckDuckGo results
      const altLinkRegex = /uddg=([^&"]+)/g;
      while ((match = altLinkRegex.exec(html)) !== null && urls.length < 3) {
        const encodedUrl = match[1];
        try {
          const decodedUrl = decodeURIComponent(encodedUrl);
          if (decodedUrl.startsWith('http') && !decodedUrl.includes('duckduckgo.com')) {
            urls.push(decodedUrl);
          }
        } catch (_e) {
          // Skip invalid URLs
          continue;
        }
      }
    }

    if (urls.length === 0) {
      throw new Error('No valid URLs found in search results');
    }

    console.log(`Found ${urls.length} URLs from DuckDuckGo search`);
    return urls.slice(0, 1); // Return only the first URL for now

  } catch (error) {
    console.error('DuckDuckGo search failed:', error);
    throw new Error(`Search failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

async function getCleanedText(urls: string[]) {
  const texts = [];
  for await (const url of urls) {
    const getUrl = await fetch(url);
    console.log(`Fetching ${url}`);
    const html = await getUrl.text();
    const text = htmlToText(html);
    texts.push(`Source: ${url}\n${text}\n\n`);
  }
  return texts;
}

function htmlToText(html: string) {
  const $ = cheerio.load(html);

  // Thanks to the comment on the YouTube video from @eliaspereirah for suggesting
  // using Mozilla Readability. I used a variant that made it easier to use with
  // cheerio. Definitely simplifies things
  const text = new Readability($).parse();

  // What I had before

  // $("script, source, style, head, img, svg, a, form, link, iframe").remove();
  // $("*").removeClass();
  // $("*").each((_, el) => {
  // 	if (el.type === "tag" || el.type === "script" || el.type === "style") {
  // 		for (const attr of Object.keys(el.attribs || {})) {
  // 			if (attr.startsWith("data-")) {
  // 				$(el).removeAttr(attr);
  // 			}
  // 		}
  // 	}
  // });
  // const text = $("body").text().replace(/\s+/g, " ");

  return text.textContent;
}

async function answerQuery(query: string, texts: string[]) {
  const result = await ollama.generate({
    model: "gemma3:1b",
    prompt:
      `${query}. Summarise the information and provide an answer. Use only the information in the following articles to answer the question: ${
        texts.join("\n\n")
      }`,
    stream: true,
    options: {
      num_ctx: 16000,
    },
  });
  for await (const chunk of result) {
    if (chunk.done !== true) {
      await Deno.stdout.write(new TextEncoder().encode(chunk.response));
    }
  }
}
